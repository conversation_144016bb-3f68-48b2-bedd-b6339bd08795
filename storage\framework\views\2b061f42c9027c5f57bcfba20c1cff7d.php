<?php $__env->startSection('title', 'Kursus Saya - Sistem Kursus'); ?>

<?php $__env->startSection('content'); ?>
<div class="container">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="mb-2">
                        <i class="fas fa-book-open me-2 text-primary"></i>Kursus Saya
                    </h1>
                    <p class="text-muted mb-0">Kelola dan pantau progress kursus yang Anda ikuti</p>
                </div>
                <div>
                    <a href="<?php echo e(route('courses.index')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>Cari Kursus Baru
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card text-center bg-light">
                <div class="card-body">
                    <i class="fas fa-book-open fa-2x text-primary mb-2"></i>
                    <h5><?php echo e($enrollments->where('status', 'active')->count()); ?></h5>
                    <small class="text-muted">Kursus Aktif</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-center bg-light">
                <div class="card-body">
                    <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                    <h5><?php echo e($enrollments->where('status', 'completed')->count()); ?></h5>
                    <small class="text-muted">Selesai</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-center bg-light">
                <div class="card-body">
                    <i class="fas fa-pause-circle fa-2x text-warning mb-2"></i>
                    <h5><?php echo e($enrollments->where('status', 'cancelled')->count()); ?></h5>
                    <small class="text-muted">Dibatalkan</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-center bg-light">
                <div class="card-body">
                    <i class="fas fa-calendar fa-2x text-info mb-2"></i>
                    <h5><?php echo e($enrollments->count()); ?></h5>
                    <small class="text-muted">Total Kursus</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Enrollments List -->
    <div class="row">
        <?php $__empty_1 = true; $__currentLoopData = $enrollments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $enrollment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card h-100 card-hover">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <h5 class="card-title"><?php echo e($enrollment->course->title); ?></h5>
                            <span class="badge bg-<?php echo e($enrollment->status == 'active' ? 'success' : ($enrollment->status == 'completed' ? 'primary' : 'secondary')); ?>">
                                <?php echo e(ucfirst($enrollment->status)); ?>

                            </span>
                        </div>
                        
                        <p class="card-text text-muted">
                            <?php echo e(Str::limit($enrollment->course->description, 100)); ?>

                        </p>
                        
                        <div class="mb-3">
                            <small class="text-muted">
                                <i class="fas fa-user me-1"></i>
                                Pengajar: <?php echo e($enrollment->course->instructor->name ?? 'Belum ditentukan'); ?>

                            </small>
                        </div>

                        <div class="mb-3">
                            <small class="text-muted">
                                <i class="fas fa-calendar me-1"></i>
                                Bergabung: <?php echo e($enrollment->enrolled_at->format('d M Y')); ?>

                            </small>
                        </div>

                        <?php if($enrollment->course->reviews_count > 0): ?>
                            <div class="mb-3">
                                <div class="d-flex align-items-center">
                                    <div class="text-warning me-2">
                                        <?php for($i = 1; $i <= 5; $i++): ?>
                                            <i class="fas fa-star<?php echo e($i <= $enrollment->course->average_rating ? '' : '-o'); ?>"></i>
                                        <?php endfor; ?>
                                    </div>
                                    <small class="text-muted">
                                        (<?php echo e($enrollment->course->reviews_count); ?> review<?php echo e($enrollment->course->reviews_count > 1 ? 's' : ''); ?>)
                                    </small>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Progress Bar (placeholder) -->
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <small class="text-muted">Progress</small>
                                <small class="text-muted">
                                    <?php if($enrollment->status == 'completed'): ?>
                                        100%
                                    <?php elseif($enrollment->status == 'active'): ?>
                                        <?php echo e(rand(20, 80)); ?>%
                                    <?php else: ?>
                                        0%
                                    <?php endif; ?>
                                </small>
                            </div>
                            <div class="progress" style="height: 6px;">
                                <div class="progress-bar bg-<?php echo e($enrollment->status == 'completed' ? 'success' : 'primary'); ?>" 
                                     style="width: <?php echo e($enrollment->status == 'completed' ? '100' : ($enrollment->status == 'active' ? rand(20, 80) : '0')); ?>%">
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card-footer bg-transparent">
                        <div class="d-grid gap-2">
                            <a href="<?php echo e(route('courses.show', $enrollment->course)); ?>" class="btn btn-outline-primary">
                                <i class="fas fa-eye me-1"></i>Lihat Detail
                            </a>
                            
                            <?php if($enrollment->status == 'active'): ?>
                                <div class="btn-group" role="group">
                                    <?php
                                        $hasReview = auth()->user()->reviews()->where('course_id', $enrollment->course->id)->exists();
                                        $hasPaid = auth()->user()->payments()->where('course_id', $enrollment->course->id)->where('status', 'verified')->exists();
                                    ?>
                                    
                                    <?php if(!$hasPaid): ?>
                                        <a href="<?php echo e(route('student.payments.create', $enrollment->course)); ?>" class="btn btn-warning btn-sm">
                                            <i class="fas fa-credit-card me-1"></i>Bayar
                                        </a>
                                    <?php endif; ?>
                                    
                                    <?php if(!$hasReview && $hasPaid): ?>
                                        <button class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#reviewModal<?php echo e($enrollment->id); ?>">
                                            <i class="fas fa-star me-1"></i>Review
                                        </button>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                            
                            <?php if($enrollment->status == 'active'): ?>
                                <form action="<?php echo e(route('student.enrollments.destroy', $enrollment)); ?>" method="POST" 
                                      onsubmit="return confirm('Apakah Anda yakin ingin membatalkan pendaftaran kursus ini?')">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit" class="btn btn-outline-danger btn-sm w-100">
                                        <i class="fas fa-times me-1"></i>Batalkan
                                    </button>
                                </form>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Review Modal for each enrollment -->
            <?php if($enrollment->status == 'active' && 
                !auth()->user()->reviews()->where('course_id', $enrollment->course->id)->exists() &&
                auth()->user()->payments()->where('course_id', $enrollment->course->id)->where('status', 'verified')->exists()): ?>
                <div class="modal fade" id="reviewModal<?php echo e($enrollment->id); ?>" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">
                                    <i class="fas fa-star me-2"></i>Review: <?php echo e($enrollment->course->title); ?>

                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <form action="<?php echo e(route('student.courses.review', $enrollment->course)); ?>" method="POST">
                                <?php echo csrf_field(); ?>
                                <div class="modal-body">
                                    <div class="mb-3">
                                        <label class="form-label">Rating</label>
                                        <div class="rating-input" data-enrollment="<?php echo e($enrollment->id); ?>">
                                            <?php for($i = 1; $i <= 5; $i++): ?>
                                                <input type="radio" name="rating" value="<?php echo e($i); ?>" id="star<?php echo e($enrollment->id); ?>_<?php echo e($i); ?>" required>
                                                <label for="star<?php echo e($enrollment->id); ?>_<?php echo e($i); ?>" class="star">
                                                    <i class="fas fa-star"></i>
                                                </label>
                                            <?php endfor; ?>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="review<?php echo e($enrollment->id); ?>" class="form-label">Review (Opsional)</label>
                                        <textarea class="form-control" id="review<?php echo e($enrollment->id); ?>" name="review" rows="4" 
                                                  placeholder="Bagikan pengalaman Anda mengikuti kursus ini..."></textarea>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-paper-plane me-1"></i>Kirim Review
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-book-open fa-4x text-muted mb-3"></i>
                    <h4 class="text-muted">Belum Ada Kursus</h4>
                    <p class="text-muted">Anda belum mendaftar kursus apapun. Mulai belajar sekarang!</p>
                    <a href="<?php echo e(route('courses.index')); ?>" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i>Jelajahi Kursus
                    </a>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- Pagination -->
    <?php if($enrollments->hasPages()): ?>
        <div class="row mt-4">
            <div class="col-12">
                <div class="d-flex justify-content-center">
                    <?php echo e($enrollments->links()); ?>

                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<style>
.rating-input {
    display: flex;
    flex-direction: row-reverse;
    justify-content: center;
}

.rating-input input {
    display: none;
}

.rating-input label {
    cursor: pointer;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: #ddd;
    transition: color 0.2s;
}

.rating-input label:hover,
.rating-input label:hover ~ label,
.rating-input input:checked ~ label {
    color: #ffc107;
}
</style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\sistem-kursus\resources\views/student/enrollments/index.blade.php ENDPATH**/ ?>