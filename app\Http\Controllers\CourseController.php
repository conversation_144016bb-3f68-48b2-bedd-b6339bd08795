<?php

namespace App\Http\Controllers;

use App\Models\Course;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CourseController extends Controller
{
    /**
     * Display a listing of the resource (Student View).
     */
    public function index()
    {
        $courses = Course::with(['instructor', 'reviews', 'enrollments'])
            ->orderBy('created_at', 'desc')
            ->get();
        return view('courses.index', compact('courses'));
    }

    /**
     * Display a listing of the resource (Admin/Teacher View).
     */
    public function admin()
    {
        $courses = Course::orderBy('created_at', 'desc')->paginate(10);
        return view('courses.admin', compact('courses'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $teachers = User::whereHas('role', function($query) {
            $query->where('name', 'Teacher');
        })->get();

        return view('courses.create', compact('teachers'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'instructor_id' => 'required|exists:users,id',
            'price' => 'required|numeric|min:0'
        ]);

        Course::create($request->all());

        return redirect()->route('admin.courses.index')
            ->with('success', 'Kursus berhasil ditambahkan!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Course $course)
    {
        return view('courses.show', compact('course'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Course $course)
    {
        return view('courses.edit', compact('course'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Course $course)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'instructor_id' => 'required|exists:users,id',
            'price' => 'required|numeric|min:0'
        ]);

        $course->update($request->all());

        return redirect()->route('admin.courses.index')
            ->with('success', 'Kursus berhasil diperbarui!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Course $course)
    {
        $course->delete();

        return redirect()->route('admin.courses.index')
            ->with('success', 'Kursus berhasil dihapus!');
    }

    /**
     * Display courses for teacher.
     */
    public function teacherCourses()
    {
        $courses = Course::where('instructor_id', Auth::id())
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('teacher.courses.index', compact('courses'));
    }

    /**
     * Register student to course.
     */
    public function register(Course $course)
    {
        // Implement registration logic here
        return redirect()->back()
            ->with('success', 'Berhasil mendaftar kursus: ' . $course->title);
    }
}