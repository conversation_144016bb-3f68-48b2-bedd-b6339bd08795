<?php

namespace App\Http\Controllers;

use App\Models\Course;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CourseController extends Controller
{
    /**
     * Display a listing of the resource (Student View).
     */
    public function index()
    {
        $courses = Course::with(['instructor', 'reviews', 'enrollments'])
            ->orderBy('created_at', 'desc')
            ->get();
        return view('courses.index', compact('courses'));
    }

    /**
     * Display a listing of the resource (Admin/Teacher View).
     */
    public function admin()
    {
        $courses = Course::orderBy('created_at', 'desc')->paginate(10);
        return view('courses.admin', compact('courses'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $teachers = User::whereHas('role', function($query) {
            $query->where('name', 'Teacher');
        })->get();

        return view('courses.create', compact('teachers'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255|unique:courses,title',
            'description' => 'required|string|min:10',
            'instructor_id' => 'required|exists:users,id',
            'price' => 'required|numeric|min:0|max:99999999'
        ], [
            'title.required' => 'Judul kursus wajib diisi.',
            'title.unique' => 'Judul kursus sudah ada, gunakan judul yang berbeda.',
            'title.max' => 'Judul kursus maksimal 255 karakter.',
            'description.required' => 'Deskripsi kursus wajib diisi.',
            'description.min' => 'Deskripsi kursus minimal 10 karakter.',
            'instructor_id.required' => 'Pengajar wajib dipilih.',
            'instructor_id.exists' => 'Pengajar yang dipilih tidak valid.',
            'price.required' => 'Harga kursus wajib diisi.',
            'price.numeric' => 'Harga harus berupa angka.',
            'price.min' => 'Harga tidak boleh kurang dari 0.',
            'price.max' => 'Harga terlalu besar.'
        ]);

        try {
            Course::create($request->all());
            return redirect()->route('admin.courses.index')
                ->with('success', 'Kursus berhasil ditambahkan!');
        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Gagal menambahkan kursus. Silakan coba lagi.');
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Course $course)
    {
        return view('courses.show', compact('course'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Course $course)
    {
        $teachers = User::whereHas('role', function($query) {
            $query->where('name', 'Teacher');
        })->get();

        return view('courses.edit', compact('course', 'teachers'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Course $course)
    {
        $request->validate([
            'title' => 'required|string|max:255|unique:courses,title,' . $course->id,
            'description' => 'required|string|min:10',
            'instructor_id' => 'required|exists:users,id',
            'price' => 'required|numeric|min:0|max:99999999'
        ], [
            'title.required' => 'Judul kursus wajib diisi.',
            'title.unique' => 'Judul kursus sudah ada, gunakan judul yang berbeda.',
            'title.max' => 'Judul kursus maksimal 255 karakter.',
            'description.required' => 'Deskripsi kursus wajib diisi.',
            'description.min' => 'Deskripsi kursus minimal 10 karakter.',
            'instructor_id.required' => 'Pengajar wajib dipilih.',
            'instructor_id.exists' => 'Pengajar yang dipilih tidak valid.',
            'price.required' => 'Harga kursus wajib diisi.',
            'price.numeric' => 'Harga harus berupa angka.',
            'price.min' => 'Harga tidak boleh kurang dari 0.',
            'price.max' => 'Harga terlalu besar.'
        ]);

        try {
            $course->update($request->all());
            return redirect()->route('admin.courses.index')
                ->with('success', 'Kursus berhasil diperbarui!');
        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Gagal memperbarui kursus. Silakan coba lagi.');
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Course $course)
    {
        try {
            // Check if course has enrollments
            if ($course->enrollments()->count() > 0) {
                return redirect()->back()
                    ->with('error', 'Tidak dapat menghapus kursus yang sudah memiliki peserta.');
            }

            $courseTitle = $course->title;
            $course->delete();

            return redirect()->route('admin.courses.index')
                ->with('success', "Kursus '{$courseTitle}' berhasil dihapus!");
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Gagal menghapus kursus. Silakan coba lagi.');
        }
    }

    /**
     * Display courses for teacher.
     */
    public function teacherCourses()
    {
        $courses = Course::where('instructor_id', Auth::id())
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('teacher.courses.index', compact('courses'));
    }

    /**
     * Register student to course.
     */
    public function register(Course $course)
    {
        // Implement registration logic here
        return redirect()->back()
            ->with('success', 'Berhasil mendaftar kursus: ' . $course->title);
    }
}