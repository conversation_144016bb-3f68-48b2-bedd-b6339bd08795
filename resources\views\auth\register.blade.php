@extends('layouts.app')

@section('title', 'Daftar - Sistem Kursus')

@section('content')
<div class="min-vh-100 d-flex align-items-center py-5" style="background: linear-gradient(135deg, var(--light-green) 0%, #fff 50%, var(--light-red) 100%);">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-7 col-xl-6">
                <div class="card border-0 shadow-lg" style="border-radius: 24px; overflow: hidden;">
                    <!-- Header with gradient -->
                    <div class="card-header text-center py-4" style="background: linear-gradient(135deg, var(--primary-red) 0%, var(--dark-teal) 100%); border: none;">
                        <div class="mb-3">
                            <div class="d-inline-flex align-items-center justify-content-center rounded-circle bg-white" style="width: 80px; height: 80px;">
                                <i class="fas fa-user-plus fa-2x" style="color: var(--primary-red);"></i>
                            </div>
                        </div>
                        <h2 class="text-white fw-bold mb-2">Bergabung Dengan Kami</h2>
                        <p class="text-white-50 mb-0">Buat akun baru untuk memulai pembelajaran</p>
                    </div>

                    <div class="card-body p-4">
                        @if ($errors->any())
                            <div class="alert alert-danger border-0 rounded-3 mb-4" style="background: var(--light-red);">
                                <div class="d-flex align-items-start">
                                    <i class="fas fa-exclamation-circle me-2 mt-1" style="color: var(--primary-red);"></i>
                                    <div>
                                        @foreach ($errors->all() as $error)
                                            <div class="mb-1">{{ $error }}</div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                        @endif

                        <form action="{{ route('register') }}" method="POST">
                            @csrf

                            <div class="row">
                                <div class="col-md-6 mb-4">
                                    <label for="name" class="form-label fw-semibold mb-2" style="color: var(--dark-teal);">
                                        <i class="fas fa-user me-2"></i>Nama Lengkap
                                    </label>
                                    <input type="text"
                                           class="form-control form-control-lg @error('name') is-invalid @enderror"
                                           id="name"
                                           name="name"
                                           value="{{ old('name') }}"
                                           placeholder="Masukkan nama lengkap"
                                           style="border-radius: 12px; border: 2px solid #e9ecef; padding: 0.875rem 1rem;"
                                           required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-6 mb-4">
                                    <label for="phone" class="form-label fw-semibold mb-2" style="color: var(--dark-teal);">
                                        <i class="fas fa-phone me-2"></i>Nomor Telepon
                                    </label>
                                    <input type="tel"
                                           class="form-control form-control-lg @error('phone') is-invalid @enderror"
                                           id="phone"
                                           name="phone"
                                           value="{{ old('phone') }}"
                                           placeholder="08xxxxxxxxxx"
                                           style="border-radius: 12px; border: 2px solid #e9ecef; padding: 0.875rem 1rem;"
                                           required>
                                    @error('phone')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="mb-4">
                                <label for="email" class="form-label fw-semibold mb-2" style="color: var(--dark-teal);">
                                    <i class="fas fa-envelope me-2"></i>Email Address
                                </label>
                                <input type="email"
                                       class="form-control form-control-lg @error('email') is-invalid @enderror"
                                       id="email"
                                       name="email"
                                       value="{{ old('email') }}"
                                       placeholder="<EMAIL>"
                                       style="border-radius: 12px; border: 2px solid #e9ecef; padding: 0.875rem 1rem;"
                                       required>
                                @error('email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-4">
                                    <label for="password" class="form-label fw-semibold mb-2" style="color: var(--dark-teal);">
                                        <i class="fas fa-lock me-2"></i>Password
                                    </label>
                                    <div class="position-relative">
                                        <input type="password"
                                               class="form-control form-control-lg @error('password') is-invalid @enderror"
                                               id="password"
                                               name="password"
                                               placeholder="Minimal 8 karakter"
                                               style="border-radius: 12px; border: 2px solid #e9ecef; padding: 0.875rem 1rem;"
                                               required>
                                        <button type="button" class="btn position-absolute end-0 top-50 translate-middle-y me-2"
                                                onclick="togglePassword('password', 'toggleIcon1')" style="border: none; background: none;">
                                            <i class="fas fa-eye" id="toggleIcon1" style="color: var(--dark-teal);"></i>
                                        </button>
                                    </div>
                                    @error('password')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-6 mb-4">
                                    <label for="password_confirmation" class="form-label fw-semibold mb-2" style="color: var(--dark-teal);">
                                        <i class="fas fa-lock me-2"></i>Konfirmasi Password
                                    </label>
                                    <div class="position-relative">
                                        <input type="password"
                                               class="form-control form-control-lg"
                                               id="password_confirmation"
                                               name="password_confirmation"
                                               placeholder="Ulangi password"
                                               style="border-radius: 12px; border: 2px solid #e9ecef; padding: 0.875rem 1rem;"
                                               required>
                                        <button type="button" class="btn position-absolute end-0 top-50 translate-middle-y me-2"
                                                onclick="togglePassword('password_confirmation', 'toggleIcon2')" style="border: none; background: none;">
                                            <i class="fas fa-eye" id="toggleIcon2" style="color: var(--dark-teal);"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-4">
                                <div class="form-check d-flex align-items-start">
                                    <input type="checkbox" class="form-check-input mt-1" id="terms" required
                                           style="border-radius: 6px;">
                                    <label class="form-check-label ms-2 text-muted" for="terms">
                                        Saya setuju dengan
                                        <a href="#" class="text-decoration-none fw-semibold" style="color: var(--primary-red);">
                                            Syarat dan Ketentuan
                                        </a>
                                        serta
                                        <a href="#" class="text-decoration-none fw-semibold" style="color: var(--primary-red);">
                                            Kebijakan Privasi
                                        </a>
                                    </label>
                                </div>
                            </div>

                            <div class="d-grid mb-4">
                                <button type="submit" class="btn btn-lg fw-semibold"
                                        style="background: linear-gradient(135deg, var(--primary-red) 0%, #e63946 100%);
                                               border: none; border-radius: 12px; padding: 1rem; color: white;
                                               transition: all 0.3s ease;">
                                    <i class="fas fa-user-plus me-2"></i>Daftar Sekarang
                                </button>
                            </div>
                        </form>

                        <div class="text-center">
                            <p class="text-muted mb-0">
                                Sudah punya akun?
                                <a href="{{ route('login') }}" class="text-decoration-none fw-semibold"
                                   style="color: var(--primary-red);">
                                    Masuk di sini
                                </a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function togglePassword(inputId, iconId) {
    const passwordInput = document.getElementById(inputId);
    const toggleIcon = document.getElementById(iconId);

    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
    }
}

// Add focus effects
document.querySelectorAll('.form-control').forEach(input => {
    input.addEventListener('focus', function() {
        this.style.borderColor = 'var(--primary-red)';
        this.style.boxShadow = '0 0 0 0.2rem rgba(255, 63, 51, 0.25)';
    });

    input.addEventListener('blur', function() {
        this.style.borderColor = '#e9ecef';
        this.style.boxShadow = 'none';
    });
});

// Button hover effect
document.querySelector('button[type="submit"]').addEventListener('mouseenter', function() {
    this.style.transform = 'translateY(-2px)';
    this.style.boxShadow = '0 8px 25px rgba(255, 63, 51, 0.3)';
});

document.querySelector('button[type="submit"]').addEventListener('mouseleave', function() {
    this.style.transform = 'translateY(0)';
    this.style.boxShadow = 'none';
});

// Password strength indicator
document.getElementById('password').addEventListener('input', function() {
    const password = this.value;
    const strengthBar = document.getElementById('strengthBar');

    if (!strengthBar) {
        const strengthContainer = document.createElement('div');
        strengthContainer.className = 'mt-2';
        strengthContainer.innerHTML = `
            <div class="progress" style="height: 4px; border-radius: 2px;">
                <div class="progress-bar" id="strengthBar" style="transition: all 0.3s ease;"></div>
            </div>
            <small class="text-muted" id="strengthText">Kekuatan password</small>
        `;
        this.parentNode.appendChild(strengthContainer);
    }

    let strength = 0;
    let text = 'Lemah';
    let color = 'var(--primary-red)';

    if (password.length >= 8) strength += 25;
    if (/[a-z]/.test(password)) strength += 25;
    if (/[A-Z]/.test(password)) strength += 25;
    if (/[0-9]/.test(password)) strength += 25;

    if (strength >= 75) {
        text = 'Kuat';
        color = 'var(--light-green)';
    } else if (strength >= 50) {
        text = 'Sedang';
        color = '#ffc107';
    }

    document.getElementById('strengthBar').style.width = strength + '%';
    document.getElementById('strengthBar').style.backgroundColor = color;
    document.getElementById('strengthText').textContent = `Kekuatan password: ${text}`;
});
</script>
@endsection
