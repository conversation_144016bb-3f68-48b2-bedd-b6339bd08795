

<?php $__env->startSection('title', '<PERSON><PERSON><PERSON> Kursus - Sistem Kursus'); ?>

<?php $__env->startSection('content'); ?>
<!-- Success/Error Messages -->
<?php if(session('success')): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i><?php echo e(session('success')); ?>

        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<?php if(session('error')): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-triangle me-2"></i><?php echo e(session('error')); ?>

        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="display-6 fw-bold text-primary mb-2">
            <i class="fas fa-cogs me-2"></i>Kelola Kursus
        </h1>
        <p class="text-muted">Manajemen semua kursus yang tersedia</p>
    </div>
    <a href="<?php echo e(route('admin.courses.create')); ?>" class="btn btn-success btn-lg">
        <i class="fas fa-plus me-2"></i>Tambah Kursus
    </a>
</div>

<?php if($courses->isEmpty()): ?>
<div class="text-center py-5">
    <div class="mb-4">
        <i class="fas fa-folder-open text-muted" style="font-size: 4rem;"></i>
    </div>
    <h3 class="text-muted">Belum Ada Kursus</h3>
    <p class="text-muted mb-4">Mulai dengan menambahkan kursus pertama Anda.</p>
    <a href="<?php echo e(route('admin.courses.create')); ?>" class="btn btn-primary btn-lg">
        <i class="fas fa-plus me-2"></i>Tambah Kursus Pertama
    </a>
</div>
<?php else: ?>
<!-- Responsive Table -->
<div class="card shadow-sm border-0">
    <div class="card-header bg-primary text-white">
        <h5 class="card-title mb-0">
            <i class="fas fa-table me-2"></i>Daftar Kursus
        </h5>
    </div>
    
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="bg-light">
                    <tr>
                        <th class="border-0 fw-semibold text-dark">#</th>
                        <th class="border-0 fw-semibold text-dark">
                            <i class="fas fa-book me-1"></i>Judul
                        </th>
                        <th class="border-0 fw-semibold text-dark">
                            <i class="fas fa-user-tie me-1"></i>Pengajar
                        </th>
                        <th class="border-0 fw-semibold text-dark">
                            <i class="fas fa-tag me-1"></i>Harga
                        </th>
                        <th class="border-0 fw-semibold text-dark">
                            <i class="fas fa-calendar me-1"></i>Tanggal Dibuat
                        </th>
                        <th class="border-0 fw-semibold text-dark text-center">
                            <i class="fas fa-cog me-1"></i>Aksi
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__currentLoopData = $courses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr>
                        <td class="align-middle">
                            <span class="badge bg-primary"><?php echo e($courses->firstItem() + $index); ?></span>
                        </td>
                        <td class="align-middle">
                            <div class="fw-semibold text-dark"><?php echo e($course->title); ?></div>
                            <small class="text-muted"><?php echo e(Str::limit($course->description, 50)); ?></small>
                        </td>
                        <td class="align-middle">
                            <span class="badge bg-info"><?php echo e($course->instructor_name); ?></span>
                        </td>
                        <td class="align-middle">
                            <span class="fw-bold text-success"><?php echo e($course->formatted_price); ?></span>
                        </td>
                        <td class="align-middle">
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i>
                                <?php echo e($course->created_at->format('d/m/Y H:i')); ?>

                            </small>
                        </td>
                        <td class="align-middle text-center">
                            <div class="btn-group" role="group">
                                <!-- Edit Button -->
                                <a href="<?php echo e(route('admin.courses.edit', $course)); ?>"
                                   class="btn btn-outline-warning btn-sm"
                                   title="Edit">
                                    <i class="fas fa-edit"></i>
                                </a>

                                <!-- Delete Button -->
                                <form action="<?php echo e(route('admin.courses.destroy', $course)); ?>"
                                      method="POST"
                                      class="d-inline delete-form">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="button"
                                            class="btn btn-outline-danger btn-sm delete-btn"
                                            title="Hapus"
                                            data-course-title="<?php echo e($course->title); ?>">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
            </table>
        </div>
    </div>
    
    <?php if($courses->hasPages()): ?>
    <div class="card-footer bg-light border-0">
        <div class="d-flex justify-content-between align-items-center">
            <div class="text-muted small">
                Menampilkan <?php echo e($courses->firstItem()); ?> sampai <?php echo e($courses->lastItem()); ?> dari <?php echo e($courses->total()); ?> hasil
            </div>
            <?php echo e($courses->links()); ?>

        </div>
    </div>
    <?php endif; ?>
</div>

<!-- Summary Cards -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white border-0">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="fw-bold"><?php echo e($courses->total()); ?></h4>
                        <p class="mb-0">Total Kursus</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-book fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white border-0">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="fw-bold"><?php echo e($courses->where('price', '>', 0)->count()); ?></h4>
                        <p class="mb-0">Kursus Berbayar</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-dollar-sign fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white border-0">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="fw-bold"><?php echo e($courses->where('price', 0)->count()); ?></h4>
                        <p class="mb-0">Kursus Gratis</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-gift fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white border-0">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="fw-bold">Rp <?php echo e(number_format($courses->avg('price'), 0, ',', '.')); ?></h4>
                        <p class="mb-0">Harga Rata-rata</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-chart-line fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<?php $__env->startPush('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Delete confirmation
    document.querySelectorAll('.delete-btn').forEach(function(button) {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            const courseTitle = this.getAttribute('data-course-title');
            const form = this.closest('.delete-form');

            Swal.fire({
                title: 'Konfirmasi Hapus',
                html: `Apakah Anda yakin ingin menghapus kursus:<br><strong>"${courseTitle}"</strong>?`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                confirmButtonText: '<i class="fas fa-trash me-1"></i>Ya, Hapus!',
                cancelButtonText: '<i class="fas fa-times me-1"></i>Batal',
                reverseButtons: true,
                customClass: {
                    confirmButton: 'btn btn-danger',
                    cancelButton: 'btn btn-secondary'
                },
                buttonsStyling: false
            }).then((result) => {
                if (result.isConfirmed) {
                    form.submit();
                }
            });
        });
    });

    // Auto hide alerts after 5 seconds
    setTimeout(function() {
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }, 5000);
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\sistem-kursus\resources\views/courses/admin.blade.php ENDPATH**/ ?>