@extends('layouts.app')

@section('title', 'Daftar Kursus - Sistem Kursus')

@section('content')
<!-- Header Section -->
<div class="row mb-5">
    <div class="col-12">
        <div class="card border-0 overflow-hidden" style="background: linear-gradient(135deg, var(--primary-red) 0%, var(--dark-teal) 100%); border-radius: 24px;">
            <div class="card-body p-5">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <div class="mb-4">
                            <h1 class="text-white fw-bold mb-3" style="font-size: 2.5rem;">
                                Jelajahi Kursus Terbaik
                            </h1>
                            <p class="text-white-50 fs-5 mb-0">
                                Temukan kursus yang tepat untuk mengembangkan skill dan meraih impian karir <PERSON>a
                            </p>
                        </div>

                        <!-- Search Bar -->
                        <div class="position-relative" style="max-width: 500px;">
                            <input type="text"
                                   class="form-control form-control-lg pe-5"
                                   placeholder="Cari kurs<PERSON>, topik, atau pengajar..."
                                   id="searchCourse"
                                   style="border-radius: 50px; border: none; padding: 1rem 1.5rem; box-shadow: 0 8px 25px rgba(0,0,0,0.1);">
                            <button class="btn position-absolute end-0 top-50 translate-middle-y me-2"
                                    type="button"
                                    style="background: var(--primary-red); color: white; border-radius: 50px; width: 45px; height: 45px;">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-4 text-end d-none d-md-block">
                        <div class="position-relative">
                            <i class="fas fa-graduation-cap text-white-50" style="font-size: 8rem; opacity: 0.2;"></i>
                            <div class="position-absolute top-0 end-0">
                                <div class="bg-white rounded-circle p-3" style="width: 60px; height: 60px;">
                                    <i class="fas fa-star" style="color: var(--light-green); font-size: 1.5rem;"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filter Tabs -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0" style="border-radius: 16px;">
            <div class="card-body p-3">
                <div class="d-flex flex-wrap gap-2">
                    <button class="btn btn-sm fw-semibold filter-btn active" data-filter="all"
                            style="border-radius: 25px; padding: 0.5rem 1.5rem;">
                        Semua Kursus
                    </button>
                    <button class="btn btn-sm fw-semibold filter-btn" data-filter="programming"
                            style="border-radius: 25px; padding: 0.5rem 1.5rem;">
                        Programming
                    </button>
                    <button class="btn btn-sm fw-semibold filter-btn" data-filter="design"
                            style="border-radius: 25px; padding: 0.5rem 1.5rem;">
                        Design
                    </button>
                    <button class="btn btn-sm fw-semibold filter-btn" data-filter="business"
                            style="border-radius: 25px; padding: 0.5rem 1.5rem;">
                        Business
                    </button>
                    <button class="btn btn-sm fw-semibold filter-btn" data-filter="marketing"
                            style="border-radius: 25px; padding: 0.5rem 1.5rem;">
                        Marketing
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Courses Grid -->
<div class="row">
    @forelse ($courses as $course)
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card h-100 border-0 course-card" style="border-radius: 20px; overflow: hidden; transition: all 0.3s ease;">
                <!-- Course Image Placeholder -->
                <div class="position-relative" style="height: 200px; background: linear-gradient(135deg, var(--light-red) 0%, var(--light-green) 100%);">
                    <div class="position-absolute top-0 end-0 m-3">
                        <span class="badge fs-6 fw-bold" style="background: var(--primary-red); color: white; border-radius: 12px; padding: 0.5rem 1rem;">
                            {{ $course->formatted_price }}
                        </span>
                    </div>
                    <div class="position-absolute bottom-0 start-0 m-3">
                        <div class="d-flex align-items-center">
                            @if($course->reviews_count > 0)
                                <div class="bg-white rounded-pill px-3 py-1 d-flex align-items-center">
                                    <i class="fas fa-star text-warning me-1"></i>
                                    <span class="fw-semibold" style="color: var(--dark-teal);">{{ number_format($course->average_rating, 1) }}</span>
                                    <small class="text-muted ms-1">({{ $course->reviews_count }})</small>
                                </div>
                            @endif
                        </div>
                    </div>
                    <div class="position-absolute top-50 start-50 translate-middle">
                        <i class="fas fa-play-circle text-white" style="font-size: 3rem; opacity: 0.7;"></i>
                    </div>
                </div>

                <div class="card-body p-4">
                    <div class="mb-3">
                        <h5 class="card-title fw-bold mb-2" style="color: var(--dark-teal);">
                            {{ $course->title }}
                        </h5>
                        <p class="card-text text-muted mb-0" style="font-size: 0.9rem; line-height: 1.5;">
                            {{ Str::limit($course->description, 120) }}
                        </p>
                    </div>

                    <div class="mb-3">
                        <div class="d-flex align-items-center">
                            <div class="bg-light rounded-circle p-2 me-3" style="width: 40px; height: 40px;">
                                <i class="fas fa-user" style="color: var(--primary-red);"></i>
                            </div>
                            <div>
                                <h6 class="mb-0 fw-semibold" style="font-size: 0.9rem;">{{ $course->instructor->name ?? 'Belum ditentukan' }}</h6>
                                <small class="text-muted">Pengajar</small>
                            </div>
                        </div>
                    </div>

                    <div class="row text-center mb-3">
                        <div class="col-4">
                            <div class="p-2">
                                <i class="fas fa-users" style="color: var(--light-green);"></i>
                                <div class="fw-semibold" style="font-size: 0.9rem;">{{ $course->total_students }}</div>
                                <small class="text-muted">Siswa</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="p-2">
                                <i class="fas fa-clock" style="color: var(--primary-red);"></i>
                                <div class="fw-semibold" style="font-size: 0.9rem;">{{ rand(4, 12) }}h</div>
                                <small class="text-muted">Durasi</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="p-2">
                                <i class="fas fa-certificate" style="color: var(--dark-teal);"></i>
                                <div class="fw-semibold" style="font-size: 0.9rem;">Ya</div>
                                <small class="text-muted">Sertifikat</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card-footer bg-transparent border-0 p-4 pt-0">
                    <div class="d-grid gap-2">
                        <a href="{{ route('courses.show', $course) }}"
                           class="btn btn-outline-primary fw-semibold"
                           style="border-radius: 12px; border: 2px solid var(--primary-red); color: var(--primary-red);">
                            <i class="fas fa-eye me-2"></i>Lihat Detail
                        </a>
                        @auth
                            @if(!auth()->user()->enrolledCourses->contains($course->id))
                                <form action="{{ route('student.courses.enroll', $course) }}" method="POST">
                                    @csrf
                                    <button type="submit" class="btn btn-lg fw-semibold w-100"
                                            style="background: var(--primary-red); color: white; border-radius: 12px; border: none;">
                                        <i class="fas fa-user-plus me-2"></i>Daftar Kursus
                                    </button>
                                </form>
                            @else
                                <button class="btn btn-lg fw-semibold w-100" disabled
                                        style="background: var(--light-green); color: white; border-radius: 12px; border: none;">
                                    <i class="fas fa-check me-2"></i>Sudah Terdaftar
                                </button>
                            @endif
                        @else
                            <a href="{{ route('login') }}" class="btn btn-lg fw-semibold"
                               style="background: var(--primary-red); color: white; border-radius: 12px; text-decoration: none;">
                                <i class="fas fa-sign-in-alt me-2"></i>Login untuk Daftar
                            </a>
                        @endauth
                    </div>
                </div>
            </div>
        </div>
    @empty
        <div class="col-12">
            <div class="text-center py-5">
                <div class="mb-4">
                    <div class="d-inline-flex align-items-center justify-content-center rounded-circle"
                         style="width: 120px; height: 120px; background: var(--light-red);">
                        <i class="fas fa-book-open fa-3x" style="color: var(--primary-red);"></i>
                    </div>
                </div>
                <h4 class="mb-3" style="color: var(--dark-teal);">Belum Ada Kursus</h4>
                <p class="text-muted mb-4">Kursus akan segera tersedia. Silakan cek kembali nanti atau hubungi admin.</p>
                <a href="{{ route('admin.courses.create') }}" class="btn btn-lg fw-semibold"
                   style="background: var(--primary-red); color: white; border-radius: 12px; text-decoration: none;">
                    <i class="fas fa-plus me-2"></i>Tambah Kursus Pertama
                </a>
            </div>
        </div>
    @endforelse
</div>

@if($courses->count() > 0)
    <!-- Stats Section -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card border-0" style="border-radius: 16px; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
                <div class="card-body text-center p-4">
                    <div class="row">
                        <div class="col-md-3">
                            <h4 class="fw-bold" style="color: var(--primary-red);">{{ $courses->count() }}</h4>
                            <p class="text-muted mb-0">Total Kursus</p>
                        </div>
                        <div class="col-md-3">
                            <h4 class="fw-bold" style="color: var(--light-green);">{{ $courses->sum('total_students') }}</h4>
                            <p class="text-muted mb-0">Total Siswa</p>
                        </div>
                        <div class="col-md-3">
                            <h4 class="fw-bold" style="color: var(--dark-teal);">{{ $courses->where('reviews_count', '>', 0)->count() }}</h4>
                            <p class="text-muted mb-0">Kursus Tereviewed</p>
                        </div>
                        <div class="col-md-3">
                            <h4 class="fw-bold text-warning">{{ number_format($courses->avg('average_rating'), 1) }}</h4>
                            <p class="text-muted mb-0">Rating Rata-rata</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endif

<style>
/* Filter buttons */
.filter-btn {
    background: #f8f9fa;
    color: var(--dark-teal);
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--primary-red);
    color: white;
    border-color: var(--primary-red);
    transform: translateY(-2px);
}

/* Course cards */
.course-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.course-card:hover .card-title {
    color: var(--primary-red) !important;
}

/* Search input focus */
#searchCourse:focus {
    box-shadow: 0 0 0 0.2rem rgba(255, 63, 51, 0.25);
    border-color: var(--primary-red);
}

/* Button hover effects */
.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Animation for course cards */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.course-card {
    animation: fadeInUp 0.6s ease forwards;
}

.course-card:nth-child(1) { animation-delay: 0.1s; }
.course-card:nth-child(2) { animation-delay: 0.2s; }
.course-card:nth-child(3) { animation-delay: 0.3s; }
.course-card:nth-child(4) { animation-delay: 0.4s; }
.course-card:nth-child(5) { animation-delay: 0.5s; }
.course-card:nth-child(6) { animation-delay: 0.6s; }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Search functionality
    const searchInput = document.getElementById('searchCourse');
    const courseCards = document.querySelectorAll('.course-card');

    searchInput.addEventListener('input', function(e) {
        const searchTerm = e.target.value.toLowerCase();

        courseCards.forEach(card => {
            const title = card.querySelector('.card-title').textContent.toLowerCase();
            const description = card.querySelector('.card-text').textContent.toLowerCase();
            const instructor = card.querySelector('.fw-semibold').textContent.toLowerCase();

            if (title.includes(searchTerm) || description.includes(searchTerm) || instructor.includes(searchTerm)) {
                card.parentElement.style.display = 'block';
                card.style.animation = 'fadeInUp 0.3s ease forwards';
            } else {
                card.parentElement.style.display = 'none';
            }
        });

        // Show "no results" message if no cards are visible
        const visibleCards = Array.from(courseCards).filter(card =>
            card.parentElement.style.display !== 'none'
        );

        if (visibleCards.length === 0 && searchTerm.length > 0) {
            showNoResultsMessage();
        } else {
            hideNoResultsMessage();
        }
    });

    // Filter functionality
    const filterButtons = document.querySelectorAll('.filter-btn');

    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons
            filterButtons.forEach(btn => btn.classList.remove('active'));
            // Add active class to clicked button
            this.classList.add('active');

            const filter = this.getAttribute('data-filter');

            courseCards.forEach(card => {
                if (filter === 'all') {
                    card.parentElement.style.display = 'block';
                } else {
                    // This is a placeholder - you would implement actual filtering logic
                    // based on course categories stored in data attributes
                    card.parentElement.style.display = 'block';
                }
            });
        });
    });

    // Smooth scroll for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
});

function showNoResultsMessage() {
    const existingMessage = document.getElementById('noResultsMessage');
    if (existingMessage) return;

    const coursesGrid = document.querySelector('.row:has(.course-card)');
    const noResultsDiv = document.createElement('div');
    noResultsDiv.id = 'noResultsMessage';
    noResultsDiv.className = 'col-12 text-center py-5';
    noResultsDiv.innerHTML = `
        <div class="mb-4">
            <div class="d-inline-flex align-items-center justify-content-center rounded-circle"
                 style="width: 100px; height: 100px; background: var(--light-red);">
                <i class="fas fa-search fa-2x" style="color: var(--primary-red);"></i>
            </div>
        </div>
        <h5 style="color: var(--dark-teal);">Tidak Ada Hasil</h5>
        <p class="text-muted">Coba gunakan kata kunci yang berbeda atau jelajahi semua kursus.</p>
    `;

    coursesGrid.appendChild(noResultsDiv);
}

function hideNoResultsMessage() {
    const existingMessage = document.getElementById('noResultsMessage');
    if (existingMessage) {
        existingMessage.remove();
    }
}
</script>

@push('scripts')
<script>
// Additional course-specific scripts can go here
console.log('Course index page loaded successfully');
</script>
@endpush
@endsection
